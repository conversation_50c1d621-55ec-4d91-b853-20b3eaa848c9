Summary
<PERSON> introduced Serval, an IT operations automation system, highlighting its features like workflow development via prompts, approval processes, and integrations with platforms such as Linear, GitHub, and AWS, while noting some limitations with AWS and custom apps. Initial use cases proposed by <PERSON> included automating new engineer onboarding, AWS access, GitHub setup, and creating Linear issues for PagerDuty, with the team addressing challenges like VPN access and sensitive information handling. <PERSON> added the team members to <PERSON><PERSON> and encouraged them to experiment with building workflows in the "Atlas infra serval test" channel, and <PERSON><PERSON><PERSON><PERSON> suggested checking the custom integration capabilities.

Details
Introduction to Ser<PERSON> <PERSON> introduced Serval, describing it as an IT operations-focused system similar to N8N, which automates requests and provides access. While Serval offers integrations with platforms like Linear, GitHub, and AWS, <PERSON> noted some limitations regarding AWS functionality and the custom app feature.
Serval Features and Capabilities <PERSON> explained that Serval workflows can be developed using prompts, leveraging Cloud 4 in the background for initial setup (00:01:31). The system allows for access to code, although the CLI for pulling down and working on code in an IDE is still in beta and did not work for them (00:02:37). <PERSON> highlighted the approval feature, which can involve a user's manager, and speculated that this functionality might be pulling information from Google Workspace due to a pre-existing connector set up by <PERSON> (00:03:41).
Proposed Initial Use Cases <PERSON> suggested starting with automating new engineer onboarding, including creating AWS credentials and adding users to GitHub teams (00:05:07). They noted that Serval can collect input from users via chat, enabling interactive workflows for gathering necessary information (00:06:19). <PERSON> <PERSON> also proposed automating AWS access and GitHub setup, creating Linear issues for PagerDuty, and testing the custom app for further integration possibilities (00:07:43).
Challenges and Considerations The team discussed challenges such as automating VPN access, as there is no API for it, which would require manual intervention or potentially triggering GitHub actions (00:10:25) (00:14:07). Guilherme Hardt raised concerns about sensitive information being sent to the Serval model, especially regarding credentials (00:11:42). Quentin Robinson clarified that while passwords might be sent for initial setup, users would be prompted to change them upon first login, and access keys would not be directly provided by the system (00:13:01).
Next Steps and Exploration Quentin Robinson added the team members to Serval and encouraged them to experiment with building workflows and exploring its capabilities in the "Atlas infra serval test" channel (00:15:07). Guilherme Hardt suggested checking the custom integration capabilities, which Anderson Borges explained would involve API integration with authentication (00:11:42) (00:18:14). Quentin Robinson also confirmed that the team has an infrastructure team set up by Michael, ensuring their experimentation will not interfere with existing work (00:16:49).

Suggested next steps
Quentin Robinson will try to hook up CircleCI integration later and figure out where to get approval from the user's manager.
Quentin Robinson will separate out tickets for new user onboarding, including creating users and sending DMs with credits for GitHub and VPN.
Quentin Robinson will add the group to Serval.
Quentin Robinson will look through the documentation for custom apps and Serval docs.
The group will play around with Serval and try to build a couple of workflows this week, and let Quentin Robinson know if they have any problems.
The group will figure out what to start with for Serval automation, possibly focusing on automating new engineer onboarding and new service requests.

