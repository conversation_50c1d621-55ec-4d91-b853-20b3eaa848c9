issues:
  - title: "[INVESTIGATION] Analyze High Volume Outgoing Network Requests from NAT Gateways"
    description: |
      # Background
      MLS provider reported abnormally high requests originating from our NAT gateways. Need to investigate the source and volume of these outgoing network requests to understand the impact and identify potential optimization opportunities.

      # Scope / Outcome
      - Identify the source applications/services generating high outgoing request volumes
      - Quantify the actual request volumes and patterns
      - Determine if the high volume is expected behavior or indicates an issue
      - Document findings and recommendations for monitoring/optimization

      # Investigation Areas
      - Review NAT gateway metrics and CloudWatch logs
      - Analyze VPC flow logs (cost-benefit analysis required)
      - Identify top request-generating pods/services
      - Review airflow-related pods specifically (mentioned as potential high-volume source)
      - Check for any retry loops or inefficient request patterns

      # Acceptance Criteria
      - Document current outgoing request volumes and patterns
      - Identify top 10 services/applications by outgoing request volume
      - Provide recommendations for request optimization if applicable
      - Create baseline metrics for ongoing monitoring
      - Report findings to MLS provider with action plan

    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "68b02031-b90a-4bf4-a593-d988c0833386"
    milestoneId: ""
